import { LitElement, html, css, Template<PERSON><PERSON>ult, PropertyValues } from "lit";
import { customElement, property } from "lit/decorators.js";
import { repeat } from "lit/directives/repeat.js";
import { styleMap } from "lit/directives/style-map.js";
import get from "lodash-es/get.js";
import "./tree-expander";
import "./tree-special-icons";
import { Node, TreeState } from "types";
import { consume } from "@lit/context";
import {
  TreeConfig,
  treeConfigContext,
  TreeController,
  treeControllerContext,
} from "./tree-helpers";

/**
 *  Folder Tree Component
 *
 *  @param { Object } node - Stores a tree
 *  @param { Number } level - level of current node
 *  @param { Boolean } isRootNode - Determines if the current node is at the root of the tree
 *  @param { String } path - Path to current node
 *  @param { String } idPath - Path to key in tree data structure
 *  @param { String } labelPath - Path to label in tree data structure
 *  @param { String } dataPath - Path to additional data in the tree structure
 *  @param { Boolean } hasDragAndDrop - Enable node drag and drop
 *  @param { Object } styleConfig - used to set styling presets such as icon position
 */

@customElement("bds-treenode")
export class BDSTreeNode<T extends Node, U = any> extends LitElement {
  @property({ type: Array })
  node: T | null = null;
  @property({ type: String })
  key: string = "";
  @property({ type: String })
  label: string = "";
  @property({ type: Number })
  level: number = 1;
  // Passed
  @property({ type: Boolean })
  isRootNode: boolean = false;
  @property({ type: Array })
  path: string[] = [];
  @property({ type: Object })
  styleConfig: {} = {};
  @property({ type: String })
  data: U | undefined;
  @property({ type: Boolean })
  isSelected: boolean = false;
  @property({ type: Boolean })
  isExpanded: boolean = false;
  // context
  @consume({ context: treeConfigContext })
  treeConfig!: TreeConfig;
  @consume({ context: treeControllerContext })
  treeController!: TreeController<T>;

  static get styles() {
    return css`
      li {
        list-style: none;
      }

      div {
        padding: var(--bds-treenode-vertical-padding) 0.5rem;
      }

      .activeClasses {
        border: 1px solid red;
        background: white;
      }
    `;
  }

  constructor() {
    super();
    this.dragInactiveHeight = "0.1rem";
    this.dragActiveHeight = "2rem";
  }

  firstUpdated(changedProps: PropertyValues) {
    this.treeController.subscribe("tree-change", this.handleUpdate);
    this.isExpanded = this.treeController.expandedKeys.has(this.key);
    this.isSelected = this.treeController.selectedKeys.has(this.key);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.treeController.unsubscribe("tree-change", this.handleUpdate);
  }

  handleUpdate = (payload: TreeState) => {
    let shouldExpand = payload.expandedKeys.has(this.key);
    let shouldSelect = payload.selectedKeys.has(this.key);

    if (this.isExpanded !== shouldExpand || this.isSelected !== shouldSelect) {
      this.isExpanded = shouldExpand;
      this.isSelected = shouldSelect;
    }
  };

  _hasChildren() {
    if (this.node && Array.isArray(this.node.children)) {
      return this.node.children.length > 0;
    }
    return false;
  }

  handleDragstart(e) {
    e.dataTransfer.setData("text/plain", this.key);
    e.dataTransfer.dropEffect = "move";
  }

  handleDragover(e) {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "move";
    e.target.style.background = "#d8e5f1";
  }

  handleDragleave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.target.style.background = "none";
  }

  handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    let node = e.target.closest("span");
    let dragKey = e.dataTransfer.getData("text/plain");
    let dropKey = node.dataset.key;
    e.target.style.background = "none";
    this._dispatchDragAndDrop(dragKey, dropKey);
  }

  handlePrevDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    e.target.style.background = "#d8e5f1";
    e.target.style.width = "100%";
    e.target.style.height = this.dragActiveHeight;
  }

  handlePrevDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.target.style.height = this.dragInactiveHeight;
    e.target.style.background = "none";
  }

  handlePrevDrop(e) {
    let dragKey = e.dataTransfer.getData("text/plain");
    let dropkey = e.target.dataset.key;
    this._dispatchPrevDragAndDrop(dragKey, dropkey);
    e.target.style.background = "none";
    e.target.style.height = this.dragInactiveHeight;
  }

  handleSiblingDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    if (e.target.dataset.key !== "ROOT") {
      e.target.style.background = "#d8e5f1";
      e.target.style.width = "100%";
      e.target.style.height = this.dragActiveHeight;
    }
  }

  handleSiblingDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    e.target.style.height = this.dragInactiveHeight;
    e.target.style.background = "none";
  }

  handleSiblingDrop(e) {
    let dragKey = e.dataTransfer.getData("text/plain");
    let dropkey = e.target.dataset.key;
    this._dispatchSiblingDragAndDrop(dragKey, dropkey);
    e.target.style.background = "none";
    e.target.style.height = this.dragInactiveHeight;
  }

  _dispatchDragAndDrop(dragKey, dropKey) {
    let options = {
      detail: {
        dragKey: dragKey,
        dropKey: dropKey,
      },
      bubbles: true,
      composed: true,
    };
    this.dispatchEvent(new CustomEvent("dragAndDrop", options));
  }

  _dispatchSiblingDragAndDrop(dragKey, dropKey) {
    let options = {
      detail: {
        dragKey: dragKey,
        dropKey: dropKey,
      },
      bubbles: true,
      composed: true,
    };
    this.dispatchEvent(new CustomEvent("siblingDragAndDrop", options));
  }

  _dispatchPrevDragAndDrop(dragKey, dropKey) {
    let options = {
      detail: {
        dragKey: dragKey,
        dropKey: dropKey,
      },
      bubbles: true,
      composed: true,
    };
    this.dispatchEvent(new CustomEvent("prevDragAndDrop", options));
  }

  _handleNodeClick(e) {
    e.target.scrollIntoView({ behavior: "smooth", block: "center" });
    this.treeController.updateTree({
      expandKey: !this.isSelected && this.isExpanded ? "" : this.key,
      selectKey: this.key,
    });
    this.treeController.dispatchTreeClick({
      label: this.label,
      key: this.key,
      hasChildren: this._hasChildren(),
      ref: this,
      isSelected: this.isSelected,
      isExpanded: this.isExpanded,
      path: this.path,
      node: this.node!,
      level: this.level,
    });
  }

  _dispatchNodeExpanded(e) {
    e.stopPropagation();
    this.treeController.updateTree({ expandKey: this.key });
  }

  _appendChildren() {
    if (!this.node) {
      return null;
    }

    if (!Array.isArray(this.node.children)) {
      return null;
    }

    if (this.isExpanded) {
      return html` <style>
          ul {
            padding: 0;
            margin: 0;
          }

          .reorder-2 {
            height: 0.1rem;
            background-color: none;
          }
        </style>
        <ul role="group">
          <div
            data-key=${`${this.key}`}
            class="reorder-2"
            @dragover=${this.handlePrevDragOver}
            @dragleave=${this.handlePrevDragLeave}
            @drop=${this.handlePrevDrop}
          ></div>
          ${repeat(
            this.node.children,
            (child) => get(child, this.treeConfig.idPath),
            (child) => html`<bds-treenode
              key=${get(child, this.treeConfig.idPath)}
              label=${get(child, this.treeConfig.labelPath)}
              .data=${get(child, this.treeConfig.dataPath)}
              .node=${child}
              .path=${[get(child, this.treeConfig.idPath)]}
              .level=${this.level + 1}
            >
            </bds-treenode>`
          )}
        </ul>`;
    }
    return;
  }

  _appendArrow() {
    return html`<tree-expander
      class="btn"
      @click=${this._dispatchNodeExpanded}
      ?isOpen=${this.isExpanded}
    >
    </tree-expander>`;
  }

  nodeTemplate() {
    const noArrowStyle = {
      padding: this._hasChildren()
        ? "0 var(--bds-treenode-vertical-padding)"
        : "0 0.95rem",
    };
    const spanStyle = {
      padding: `var(--bds-treenode-vertical-padding, 0.5rem) 0.5rem var(--bds-treenode-vertical-padding, 0.5rem) ${
        this.level * 0.5
      }rem`,
      background: this.isSelected
        ? "var(--bds-treenode-active, #c7d9e6a3)"
        : null,
    };
    return html`
      <span
        class="node-container"
        part="node-container"
        style=${styleMap(spanStyle)}
        draggable=${Boolean(this.hasDragAndDrop)}
        @dragover=${this.handleDragover}
        @dragleave=${this.handleDragleave}
        @drop=${this.handleDrop}
        @dragstart=${this.handleDragstart}
        @click=${this._handleNodeClick}
      >
        ${!this.treeConfig.styleConfig?.hasIconBefore
          ? html`
              <div style="padding-left: 0.5rem">${this.label}</div>
              ${this._hasChildren() ? this._appendArrow() : null}
            `
          : html`
              ${this._hasChildren() ? this._appendArrow() : null}
              <div style=${styleMap(noArrowStyle)}>
                <tree-special-icons
                  .icon=${this.node?.type}
                  .iconSet="ditamap"
                ></tree-special-icons>
                ${this.label}
              </div>
            `}
      </span>
    `;
  }

  applyContextMenu(template: TemplateResult) {
    let node = this;
    if (this.treeConfig.contextMenuRenderer) {
      return this.treeConfig.contextMenuRenderer(template, node);
    }
    return template;
  }

  render() {
    return html`
      <style>
        li {
          text-indent: 0;
          margin: 0;
          cursor: pointer;
        }

        iron-icon {
          width: fit-content;
          max-width: 1rem;
          margin-right: 0.01rem;
        }

        .btn {
          position: relative;
          z-index: 1000;
        }

        .btn:hover {
          color: var(--bds-treenode-arrow-hover, lightblue);
        }

        span:hover {
          background-color: var(--bds-treenode-hover, #d5e9f8a3);
        }

        .node-container {
          display: flex;
          align-items: center;
          justify-content: ${`${
            this.treeConfig.styleConfig?.hasIconBefore
              ? "flex-start"
              : "space-between"
          }`};
          padding-left: ${`${this.level * 0.625}rem`};
        }

        .reorder {
          height: 0.1rem;
          background-color: none;
        }
      </style>
      <li
        role="treeitem"
        part=${this.isRootNode ? "root" : "node"}
        aria-expanded=${this._hasChildren() ? String(this.isExpanded) : null}
        aria-selected=${this._hasChildren() ? String(this.isSelected) : null}
        data-key=${this.key}
        tabindex="0"
      >
        ${this.applyContextMenu(this.nodeTemplate())} ${this._appendChildren()}
      </li>
      <div
        data-key=${`${this.key}`}
        class="reorder"
        @dragover=${this.handleSiblingDragOver}
        @dragleave=${this.handleSiblingDragLeave}
        @drop=${this.handleSiblingDrop}
      ></div>
    `;
  }

  createRenderRoot() {
    return this;
  }
}
