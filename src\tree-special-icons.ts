import { LitElement, html } from "lit";
import { customElement, property } from "lit/decorators.js";

@customElement("tree-special-icons")
export class BDSTreeSpecialIcons extends LitElement {
  @property({ type: String })
  icon: DitaMapType = "reference";
  @property({ type: String })
  iconSet: string = "";

  ditamapIcon() {
    if (this.iconSet !== "ditamap") {
      return html``;
    }
    
    switch (this.icon) {
      case "bookmap":
        return html`<svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 640 640"
        >
          <!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.-->
          <path
            d="M320 205.3L320 514.6L320.5 514.4C375.1 491.7 433.7 480 492.8 480L512 480L512 160L492.8 160C450.6 160 408.7 168.4 369.7 184.6C352.9 191.6 336.3 198.5 320 205.3zM294.9 125.5L320 136L345.1 125.5C391.9 106 442.1 96 492.8 96L528 96C554.5 96 576 117.5 576 144L576 496C576 522.5 554.5 544 528 544L492.8 544C442.1 544 391.9 554 345.1 573.5L332.3 578.8C324.4 582.1 315.6 582.1 307.7 578.8L294.9 573.5C248.1 554 197.9 544 147.2 544L112 544C85.5 544 64 522.5 64 496L64 144C64 117.5 85.5 96 112 96L147.2 96C197.9 96 248.1 106 294.9 125.5z"
          />
        </svg>`;
      case "map":
        return html`<svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 640 640"
        >
          <!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.-->
          <path
            d="M280 152L360 152L360 200L280 200L280 152zM272 96C245.5 96 224 117.5 224 144L224 208C224 234.5 245.5 256 272 256L288 256L288 288L64 288C46.3 288 32 302.3 32 320C32 337.7 46.3 352 64 352L160 352L160 384L144 384C117.5 384 96 405.5 96 432L96 496C96 522.5 117.5 544 144 544L240 544C266.5 544 288 522.5 288 496L288 432C288 405.5 266.5 384 240 384L224 384L224 352L416 352L416 384L400 384C373.5 384 352 405.5 352 432L352 496C352 522.5 373.5 544 400 544L496 544C522.5 544 544 522.5 544 496L544 432C544 405.5 522.5 384 496 384L480 384L480 352L576 352C593.7 352 608 337.7 608 320C608 302.3 593.7 288 576 288L352 288L352 256L368 256C394.5 256 416 234.5 416 208L416 144C416 117.5 394.5 96 368 96L272 96zM480 440L488 440L488 488L408 488L408 440L480 440zM224 440L232 440L232 488L152 488L152 440L224 440z"
          />
        </svg>`;
      case "glossentry":
        return html`<svg
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          fill="#000000"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></g>
          <g id="SVGRepo_iconCarrier">
            <defs>
              <clipPath id="clip-glossary">
                <rect width="32" height="32"></rect>
              </clipPath>
            </defs>
            <g id="glossary" clip-path="url(#clip-glossary)">
              <g
                id="Group_3020"
                data-name="Group 3020"
                transform="translate(-312 -156)"
              >
                <g id="Group_2980" data-name="Group 2980">
                  <g id="Group_2979" data-name="Group 2979">
                    <g id="Group_2978" data-name="Group 2978">
                      <g id="Group_2977" data-name="Group 2977">
                        <g id="Group_2976" data-name="Group 2976">
                          <g id="Group_2975" data-name="Group 2975">
                            <path
                              id="Path_3962"
                              data-name="Path 3962"
                              d="M336.292,186.917H319.709A3.921,3.921,0,0,1,315.792,183V161a3.921,3.921,0,0,1,3.917-3.917h16.583A3.922,3.922,0,0,1,340.209,161v22A3.922,3.922,0,0,1,336.292,186.917Zm-16.583-27.834A1.919,1.919,0,0,0,317.792,161v22a1.919,1.919,0,0,0,1.917,1.917h16.583A1.919,1.919,0,0,0,338.209,183V161a1.919,1.919,0,0,0-1.917-1.917Z"
                              fill="#344952"
                            ></path>
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
                <g id="Group_3019" data-name="Group 3019">
                  <g id="Group_2987" data-name="Group 2987">
                    <g id="Group_2986" data-name="Group 2986">
                      <g id="Group_2985" data-name="Group 2985">
                        <g id="Group_2984" data-name="Group 2984">
                          <g id="Group_2983" data-name="Group 2983">
                            <g id="Group_2982" data-name="Group 2982">
                              <g id="Group_2981" data-name="Group 2981">
                                <path
                                  id="Path_3963"
                                  data-name="Path 3963"
                                  d="M321.774,162.4h1.572l2.295,6.488H324.2l-.439-1.32H321.3l-.425,1.32h-1.4Zm-.094,4.052H323.4l-.861-2.609h0Z"
                                  fill="#344952"
                                ></path>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                  <g id="Group_3018" data-name="Group 3018">
                    <g id="Group_2997" data-name="Group 2997">
                      <g id="Group_2996" data-name="Group 2996">
                        <g id="Group_2995" data-name="Group 2995">
                          <g id="Group_2994" data-name="Group 2994">
                            <g id="Group_2993" data-name="Group 2993">
                              <g id="Group_2992" data-name="Group 2992">
                                <g id="Group_2991" data-name="Group 2991">
                                  <g id="Group_2990" data-name="Group 2990">
                                    <g id="Group_2989" data-name="Group 2989">
                                      <g id="Group_2988" data-name="Group 2988">
                                        <path
                                          id="Path_3964"
                                          data-name="Path 3964"
                                          d="M335.479,173.726h-15a1,1,0,0,1,0-2h15a1,1,0,0,1,0,2Z"
                                          fill="#344952"
                                        ></path>
                                      </g>
                                    </g>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </g>
                    <g id="Group_3007" data-name="Group 3007">
                      <g id="Group_3006" data-name="Group 3006">
                        <g id="Group_3005" data-name="Group 3005">
                          <g id="Group_3004" data-name="Group 3004">
                            <g id="Group_3003" data-name="Group 3003">
                              <g id="Group_3002" data-name="Group 3002">
                                <g id="Group_3001" data-name="Group 3001">
                                  <g id="Group_3000" data-name="Group 3000">
                                    <g id="Group_2999" data-name="Group 2999">
                                      <g id="Group_2998" data-name="Group 2998">
                                        <path
                                          id="Path_3965"
                                          data-name="Path 3965"
                                          d="M335.479,177.684h-15a1,1,0,0,1,0-2h15a1,1,0,0,1,0,2Z"
                                          fill="#344952"
                                        ></path>
                                      </g>
                                    </g>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </g>
                    <g id="Group_3017" data-name="Group 3017">
                      <g id="Group_3016" data-name="Group 3016">
                        <g id="Group_3015" data-name="Group 3015">
                          <g id="Group_3014" data-name="Group 3014">
                            <g id="Group_3013" data-name="Group 3013">
                              <g id="Group_3012" data-name="Group 3012">
                                <g id="Group_3011" data-name="Group 3011">
                                  <g id="Group_3010" data-name="Group 3010">
                                    <g id="Group_3009" data-name="Group 3009">
                                      <g id="Group_3008" data-name="Group 3008">
                                        <path
                                          id="Path_3966"
                                          data-name="Path 3966"
                                          d="M335.521,181.6h-15a1,1,0,0,1,0-2h15a1,1,0,0,1,0,2Z"
                                          fill="#344952"
                                        ></path>
                                      </g>
                                    </g>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </svg>`;
      case "concept":
        return html`<svg
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></g>
          <g id="SVGRepo_iconCarrier">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M9.29289 1.29289C9.48043 1.10536 9.73478 1 10 1H18C19.6569 1 21 2.34315 21 4V20C21 21.6569 19.6569 23 18 23H6C4.34315 23 3 21.6569 3 20V8C3 7.73478 3.10536 7.48043 3.29289 7.29289L9.29289 1.29289ZM18 3H11V8C11 8.55228 10.5523 9 10 9H5V20C5 20.5523 5.44772 21 6 21H18C18.5523 21 19 20.5523 19 20V4C19 3.44772 18.5523 3 18 3ZM6.41421 7H9V4.41421L6.41421 7ZM7 13C7 12.4477 7.44772 12 8 12H16C16.5523 12 17 12.4477 17 13C17 13.5523 16.5523 14 16 14H8C7.44772 14 7 13.5523 7 13ZM7 17C7 16.4477 7.44772 16 8 16H16C16.5523 16 17 16.4477 17 17C17 17.5523 16.5523 18 16 18H8C7.44772 18 7 17.5523 7 17Z"
              fill="#000000"
            ></path>
          </g>
        </svg>`;
      case "task":
        return html`<svg
          xmlns:dc="http://purl.org/dc/elements/1.1/"
          xmlns:cc="http://creativecommons.org/ns#"
          xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
          xmlns:svg="http://www.w3.org/2000/svg"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
          xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
          viewBox="0 0 400 400.00001"
          id="svg23475"
          version="1.1"
          inkscape:version="0.91 r13725"
          sodipodi:docname="task.svg"
          fill="#000000"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></g>
          <g id="SVGRepo_iconCarrier">
            <defs id="defs23477"></defs>
            <sodipodi:namedview
              id="base"
              pagecolor="#ffffff"
              bordercolor="#666666"
              borderopacity="1.0"
              inkscape:pageopacity="0.0"
              inkscape:pageshadow="2"
              inkscape:zoom="0.35"
              inkscape:cx="375"
              inkscape:cy="520"
              inkscape:document-units="px"
              inkscape:current-layer="layer1"
              showgrid="false"
              units="px"
              inkscape:window-width="1920"
              inkscape:window-height="1056"
              inkscape:window-x="1920"
              inkscape:window-y="24"
              inkscape:window-maximized="1"
            ></sodipodi:namedview>
            <metadata id="metadata23480">
              <rdf:rdf>
                <cc:work rdf:about="">
                  <dc:format>image/svg+xml</dc:format>
                  <dc:type
                    rdf:resource="http://purl.org/dc/dcmitype/StillImage"
                  ></dc:type>
                  <dc:title> </dc:title>
                </cc:work>
              </rdf:rdf>
            </metadata>
            <g
              inkscape:label="Capa 1"
              inkscape:groupmode="layer"
              id="layer1"
              transform="translate(0,-652.36216)"
            >
              <path
                inkscape:connector-curvature="0"
                style="opacity:1;fill:#000000;fill-opacity:1;stroke:none;stroke-width:25;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
                d="m 162.2617,652.15012 0,28.6992 -27.7617,0.01 0,-28.7051 -25,0 0,28.707 -27,0 0,-0.043 -25,0 0,0.043 0,25 0,45 0,25 0,251.71298 0,25 25,0 235,0 23.2148,0 1.7852,0 0,-171.57038 0,-200.1855 -51.2383,0.01 0,-28.6777 -25,0 0,28.6816 -27.7617,0.01 0,-28.6875 -25,0 0,28.6914 -26.2383,0 0,-28.6953 -25,0 z M 82.5,705.86491 l 27,0 0,21.2872 25,0 0,-21.2872 27.7617,0 0,21.2872 25,0 0,-21.2872 26.2383,0 0,21.2872 25,0 0,-21.2872 27.7617,0 0,21.2872 25,0 0,-21.2872 26.2383,0 0,45 -235,0 0,-45 z m 0,70 235,0 0,112.1426 0,139.57039 -235,0 0,-251.71299 z m 25.4551,30.252 0,25 30.6074,0 0,-25 -30.6074,0 z m 55.6074,0 0,25 127.5332,0 0,-25 -127.5332,0 z m -55.6074,58 0,25 30.6074,0 0,-25 -30.6074,0 z m 55.6074,0 0,25 127.4883,0 0,-25 -127.4883,0 z m -55.6074,58 0,25 30.6074,0 0,-25 -30.6074,0 z m 55.6074,0 0,25 127.082,0 0,-25 -127.082,0 z m -55.6074,56 0,25.00009 30.6074,0 0,-25.00009 -30.6074,0 z m 55.6074,0 0,25.00009 127.5078,0 0,-25.00009 -127.5078,0 z"
                id="task"
              >
                <title id="title23459">task</title>
              </path>
            </g>
          </g>
        </svg>`;
    }
  }

  render() {
    return html`
      <style>
        svg {
          width: 1rem;
          height: 1rem;
        }
      </style>
      <span> ${this.ditamapIcon()} </span>
    `;
  }

  createRenderRoot() {
    return this;
  }
}

type DitaMapType =
  | "map"
  | "bookmap"
  | "subjectScheme"
  | "task"
  | "learning"
  | "concept"
  | "glossentry"
  | "reference"
  | "unknown";
