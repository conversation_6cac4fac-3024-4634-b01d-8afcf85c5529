import { BDSTreeNode } from "bds-treenode";
import { TemplateResult } from "lit";

export interface Node {
  children?: Node[];
  type?: string;
}

export interface TreeEventData<T = any> {
  key: string;
  label: string;
  hasChildren: boolean;
  isSelected: boolean;
  isExpanded: boolean;
  level: number;
  ref: BDSTreeNode<T extends Node>;
  path: string[];
  node: T;
}

export interface TreeState {
  selectedKeys: Set<string>;
  expandedKeys: Set<string>;
}

export interface Config {
  dataPath?: string;
  showRoot?: boolean;
  idPath?: string;
  labelPath?: string;
  childPath?: string;
  isMultiSelect?: boolean;
  hasUnselect?: boolean;
  hasDragAndDrop?: boolean;
  initialNodeId?: string;
  styleConfig?: StyleConfig;
  iconSet?: string;
  contextMenuRenderer?: (
    node: any,
    event: MouseEvent
  ) => HTMLElement | TemplateResult | void;
}

export interface StyleConfig {
  hasIconBefore: boolean;
}

export type Subscriber = (payload: any) => void;

export interface TreeClickDetail<T> {
  key: string;
  label: string;
  data: T;
}

